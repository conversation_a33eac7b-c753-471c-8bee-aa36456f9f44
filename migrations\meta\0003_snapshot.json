{"id": "88a67371-ad49-4096-8302-c4a2dda9848a", "prevId": "f452f7ba-d1e2-4aec-976c-75bb18ed10db", "version": "7", "dialect": "postgresql", "tables": {"public.account": {"name": "account", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "account_id": {"name": "account_id", "type": "text", "primaryKey": false, "notNull": true}, "provider_id": {"name": "provider_id", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false}, "id_token": {"name": "id_token", "type": "text", "primaryKey": false, "notNull": false}, "access_token_expires_at": {"name": "access_token_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "refresh_token_expires_at": {"name": "refresh_token_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "scope": {"name": "scope", "type": "text", "primaryKey": false, "notNull": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"account_user_id_user_id_fk": {"name": "account_user_id_user_id_fk", "tableFrom": "account", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.session": {"name": "session", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "ip_address": {"name": "ip_address", "type": "text", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "impersonated_by": {"name": "impersonated_by", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"session_user_id_user_id_fk": {"name": "session_user_id_user_id_fk", "tableFrom": "session", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"session_token_unique": {"name": "session_token_unique", "nullsNotDistinct": false, "columns": ["token"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user": {"name": "user", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "email_verified": {"name": "email_verified", "type": "boolean", "primaryKey": false, "notNull": true}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": false}, "banned": {"name": "banned", "type": "boolean", "primaryKey": false, "notNull": false}, "ban_reason": {"name": "ban_reason", "type": "text", "primaryKey": false, "notNull": false}, "ban_expires": {"name": "ban_expires", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_email_unique": {"name": "user_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.verification": {"name": "verification", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "identifier": {"name": "identifier", "type": "text", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.notifications": {"name": "notifications", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "message": {"name": "message", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true, "default": "'order_status'"}, "order_id": {"name": "order_id", "type": "uuid", "primaryKey": false, "notNull": false}, "comment_id": {"name": "comment_id", "type": "uuid", "primaryKey": false, "notNull": false}, "is_read": {"name": "is_read", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "read_at": {"name": "read_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"notifications_user_id_user_id_fk": {"name": "notifications_user_id_user_id_fk", "tableFrom": "notifications", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "notifications_order_id_orders_id_fk": {"name": "notifications_order_id_orders_id_fk", "tableFrom": "notifications", "tableTo": "orders", "columnsFrom": ["order_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "notifications_comment_id_order_comments_id_fk": {"name": "notifications_comment_id_order_comments_id_fk", "tableFrom": "notifications", "tableTo": "order_comments", "columnsFrom": ["comment_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.order_comments": {"name": "order_comments", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "order_id": {"name": "order_id", "type": "uuid", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "author_id": {"name": "author_id", "type": "text", "primaryKey": false, "notNull": true}, "is_internal": {"name": "is_internal", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "visible_to_roles": {"name": "visible_to_roles", "type": "text", "primaryKey": false, "notNull": false}, "parent_comment_id": {"name": "parent_comment_id", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"order_comments_order_id_orders_id_fk": {"name": "order_comments_order_id_orders_id_fk", "tableFrom": "order_comments", "tableTo": "orders", "columnsFrom": ["order_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "order_comments_author_id_user_id_fk": {"name": "order_comments_author_id_user_id_fk", "tableFrom": "order_comments", "tableTo": "user", "columnsFrom": ["author_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "order_comments_parent_comment_id_order_comments_id_fk": {"name": "order_comments_parent_comment_id_order_comments_id_fk", "tableFrom": "order_comments", "tableTo": "order_comments", "columnsFrom": ["parent_comment_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.order_history": {"name": "order_history", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "order_id": {"name": "order_id", "type": "uuid", "primaryKey": false, "notNull": true}, "action": {"name": "action", "type": "text", "primaryKey": false, "notNull": true}, "from_status": {"name": "from_status", "type": "text", "primaryKey": false, "notNull": false}, "to_status": {"name": "to_status", "type": "text", "primaryKey": false, "notNull": false}, "field_changes": {"name": "field_changes", "type": "text", "primaryKey": false, "notNull": false}, "performed_by": {"name": "performed_by", "type": "text", "primaryKey": false, "notNull": false}, "reason": {"name": "reason", "type": "text", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"order_history_order_id_orders_id_fk": {"name": "order_history_order_id_orders_id_fk", "tableFrom": "order_history", "tableTo": "orders", "columnsFrom": ["order_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "order_history_performed_by_user_id_fk": {"name": "order_history_performed_by_user_id_fk", "tableFrom": "order_history", "tableTo": "user", "columnsFrom": ["performed_by"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.order_items": {"name": "order_items", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "order_id": {"name": "order_id", "type": "uuid", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "sku": {"name": "sku", "type": "text", "primaryKey": false, "notNull": false}, "price": {"name": "price", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "quantity": {"name": "quantity", "type": "integer", "primaryKey": false, "notNull": true}, "quantity_shipped": {"name": "quantity_shipped", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "quantity_returned": {"name": "quantity_returned", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"order_items_order_id_orders_id_fk": {"name": "order_items_order_id_orders_id_fk", "tableFrom": "order_items", "tableTo": "orders", "columnsFrom": ["order_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.orders": {"name": "orders", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "order_number": {"name": "order_number", "type": "text", "primaryKey": false, "notNull": true}, "customer_name": {"name": "customer_name", "type": "text", "primaryKey": false, "notNull": true}, "customer_email": {"name": "customer_email", "type": "text", "primaryKey": false, "notNull": false}, "customer_phone": {"name": "customer_phone", "type": "text", "primaryKey": false, "notNull": false}, "customer_address": {"name": "customer_address", "type": "text", "primaryKey": false, "notNull": false}, "total": {"name": "total", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'pending'"}, "created_by": {"name": "created_by", "type": "text", "primaryKey": false, "notNull": false}, "updated_by": {"name": "updated_by", "type": "text", "primaryKey": false, "notNull": false}, "approved_by": {"name": "approved_by", "type": "text", "primaryKey": false, "notNull": false}, "approved_at": {"name": "approved_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "rejection_reason": {"name": "rejection_reason", "type": "text", "primaryKey": false, "notNull": false}, "edit_request_reason": {"name": "edit_request_reason", "type": "text", "primaryKey": false, "notNull": false}, "warehouse_confirmed_by": {"name": "warehouse_confirmed_by", "type": "text", "primaryKey": false, "notNull": false}, "warehouse_confirmed_at": {"name": "warehouse_confirmed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "warehouse_rejection_reason": {"name": "warehouse_rejection_reason", "type": "text", "primaryKey": false, "notNull": false}, "shipped_by": {"name": "shipped_by", "type": "text", "primaryKey": false, "notNull": false}, "shipped_at": {"name": "shipped_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "tracking_number": {"name": "tracking_number", "type": "text", "primaryKey": false, "notNull": false}, "shipping_notes": {"name": "shipping_notes", "type": "text", "primaryKey": false, "notNull": false}, "completed_at": {"name": "completed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "completion_notes": {"name": "completion_notes", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"orders_created_by_user_id_fk": {"name": "orders_created_by_user_id_fk", "tableFrom": "orders", "tableTo": "user", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "orders_updated_by_user_id_fk": {"name": "orders_updated_by_user_id_fk", "tableFrom": "orders", "tableTo": "user", "columnsFrom": ["updated_by"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "orders_approved_by_user_id_fk": {"name": "orders_approved_by_user_id_fk", "tableFrom": "orders", "tableTo": "user", "columnsFrom": ["approved_by"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "orders_warehouse_confirmed_by_user_id_fk": {"name": "orders_warehouse_confirmed_by_user_id_fk", "tableFrom": "orders", "tableTo": "user", "columnsFrom": ["warehouse_confirmed_by"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "orders_shipped_by_user_id_fk": {"name": "orders_shipped_by_user_id_fk", "tableFrom": "orders", "tableTo": "user", "columnsFrom": ["shipped_by"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"orders_order_number_unique": {"name": "orders_order_number_unique", "nullsNotDistinct": false, "columns": ["order_number"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}